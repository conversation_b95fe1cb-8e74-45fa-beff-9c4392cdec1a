#!/bin/bash
#
# ocs-flyright - Flyright Aviation Clonezilla Backup/Restore System
#
# Author: <PERSON> <steven _at_ clonezilla org> (Original Clonezilla)
#         Modified for Flyright Aviation use case
# License: GPL
#
# Description:
#   This script provides a customized Clonezilla interface for Flyright Aviation's
#   simulator drive backup and restore operations. It integrates with a SQLite database
#   to track drive information, revisions, and provides automated device detection
#   for various simulator types including C208, EFIS, KA G1000, C90, and PL21 systems.
#
# Features:
#   - Automated device detection based on serial numbers
#   - Database integration for drive tracking and revision management
#   - Support for both regular hard drives and SD cards with UHS-II optimization
#   - Archive disk selection and mounting with multiple simulator support
#   - SCSI2SD configuration parsing for SD card operations
#   - Comprehensive logging and standardized error handling
#   - Smart backup sizing based on previous backups or SCSI2SD configuration
#
# Usage:
#   Run this script directly to start the interactive backup/restore process
#   The script will guide you through:
#   1. Archive disk selection
#   2. Drive type selection (HDD vs SD Card)
#   3. Operation type (Backup vs Restore)
#   4. Target device selection

# Store original arguments for restart functionality
original_args=("$@")
#   5. Execution with progress monitoring

# ============================================================================
# INITIALIZATION AND CONFIGURATION
# ============================================================================

# Load DRBL (Diskless Remote Boot in Linux) settings and functions
DRBL_SCRIPT_PATH="${DRBL_SCRIPT_PATH:-/usr/share/drbl}"

# Source required configuration and function files
. $DRBL_SCRIPT_PATH/sbin/drbl-conf-functions
. /etc/drbl/drbl-ocs.conf
. $DRBL_SCRIPT_PATH/sbin/ocs-functions
. /home/<USER>/Desktop/clonezilla-master/usr/sbin/ocs-flyright-functions

# Load Clonezilla Live settings if available
[ -e /etc/ocs/ocs-live.conf ] && . /etc/ocs/ocs-live.conf

# Set language to English UTF-8
ask_and_load_lang_set en_US.UTF-8

# ============================================================================
# GLOBAL VARIABLES AND CONFIGURATION
# ============================================================================

# Database configuration
database_path="/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"
table="Drives"

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

#
# error_exit() - Display error message and exit
#
# Parameters:
#   $1 - Error message to display
#
# Description:
#   Shows an error message and exits the script with status 1
#
error_exit() {
    echo "Error: $1" >&2
    exit 1
}

# ============================================================================
# CORE FUNCTIONS
# ============================================================================

#
# find_dev() - Look up device information in the database
#
# Parameters:
#   $1 - Target device path (e.g., /dev/sda) OR serial number
#   $2 - Optional: "by_serial" to indicate $1 is a serial number
#
# Returns:
#   Echoes: "simulator computer letter serial_number"
#   Exit code: 0 on success, 1 on error
#
# Description:
#   Retrieves device information from database. Can work with device path
#   (extracts serial using hdparm) or directly with serial number.
#
find_dev() {
    local input="$1"
    local mode="$2"
    local serial_number=""

    # Validate input parameter
    if [ -z "$input" ]; then
        echo "Error: No device or serial number specified"
        return 1
    fi

    # Determine serial number based on input mode
    if [ "$mode" = "by_serial" ]; then
        serial_number="$input"
    else
        # Check if device exists
        if [ ! -b "$input" ]; then
            echo "Error: Device $input does not exist or is not a block device"
            return 1
        fi

        # Retrieve the serial number using hdparm
        serial_number=$(sudo hdparm -I "$input" 2>/dev/null | grep "Serial Number" | awk '{print $3}')

        if [ -z "$serial_number" ]; then
            echo "Error: Serial number not found for device $input"
            return 1
        fi
    fi

    # Check if database exists
    if [ ! -f "$database_path" ]; then
        echo "Error: Database file $database_path not found"
        return 1
    fi

    # Query the SQLite database with the serial number
    local output2=$(sqlite3 "$database_path" <<EOF 2>/dev/null
.header off
.mode list
SELECT "Computer", "Revision Number", "Sibling Drives", "Simulator", "Letter/PriSec" FROM $table WHERE "Serial Number" = '$serial_number';
EOF
    )

    # Check if database query was successful
    if [ $? -ne 0 ]; then
        echo "Error: Database query failed"
        return 1
    fi

    local computer=$(echo "$output2" | awk -F '|' '{print $1}')
    local simulator=$(echo "$output2" | awk -F '|' '{print $4}')
    local letter=$(echo "$output2" | awk -F '|' '{print $5}')

    echo "$simulator $computer $letter $serial_number"
}



# Device serial numbers and their corresponding variables
declare -A device_serials=(
    ["c208"]="Z4Z83RHP"
    ["efis"]="9WM89ES0"
    ["ka_g1000"]="Z1E1T00R"
    ["c90"]="Z4Z9FNMZ"
    ["pl21"]="Z240DZ7R"
    ["dash"]="Z4E0B6E2"
    ["internal"]="WD-WCC6Y4EEKNNV"
    ["os"]="NJD518W1140090S39C"
)

declare -A device_paths=(
    ["c208"]="not found"
    ["efis"]="not found"
    ["ka_g1000"]="not found"
    ["c90"]="not found"
    ["pl21"]="not found"
    ["dash"]="not found"
    ["internal"]="not found"
    ["os"]="not found"
)

otherTMPdevs="$(mktemp /tmp/devs.XXXXXX)"

# ============================================================================
# ARCHIVE DISK FUNCTIONS
# ============================================================================

#
# scan_and_select_archive_disk() - Detect and categorize storage devices
#
# Description:
#   Scans all /dev/sd* devices, retrieves their serial numbers using hdparm,
#   and matches them against known device serials to populate device_paths array.
#   This enables automatic detection of archive disks for different simulators.
#   Unknown devices are logged for debugging purposes.
#
# Global Variables Modified:
#   - device_paths: Updated with detected device paths
#   - otherTMPdevs: Populated with unrecognized devices
#
scan_and_select_archive_disk() {
    # Check if /dev/sd* devices exist
    if ! ls /dev/sd* >/dev/null 2>&1; then
        echo "Error: No SCSI/SATA devices found"
        return 1
    fi

    local dev_list=""

    for file in /dev/sd*; do
        # Skip if not a block device
        if [ ! -b "$file" ]; then
            continue
        fi

        local dev="${file}"
        # Get the HDD serial number with error handling
        local hdd_serial=$(sudo hdparm -I "$dev" 2>/dev/null | grep "Serial Number" | awk '{print $3}')

        # Debug output
        echo "DEBUG: Device $dev has serial: '$hdd_serial'"

        # Check if this serial matches any known device
        local found=false
        for device_name in "${!device_serials[@]}"; do
            if [ "$hdd_serial" == "${device_serials[$device_name]}" ]; then
                device_paths["$device_name"]="$dev"
                echo "DEBUG: Matched $device_name with serial $hdd_serial at $dev"
                found=true
                break
            fi
        done

        # If not found in known devices, add to other devices list
        if [ "$found" = false ]; then
            dev_list="$dev_list $dev"
        fi
    done

    # Debug output - show what devices were found
    echo "DEBUG: Device detection results:"
    for device_name in "${!device_paths[@]}"; do
        echo "DEBUG: $device_name -> ${device_paths[$device_name]}"
    done
    echo "DEBUG: Other devices: $dev_list"

    # Check if temp file can be created
    if ! echo "$dev_list" > "$otherTMPdevs" 2>/dev/null; then
        echo "Error: Cannot write to temporary file $otherTMPdevs"
        return 1
    fi
}

# ============================================================================
# USER INTERFACE FUNCTIONS
# ============================================================================

sd_card_or_hd() {
    local TMP="$(mktemp /tmp/menu.XXXXXX)"
    trap "[ -f \"$TMP\" ] && rm -f \"$TMP\"" HUP INT QUIT TERM EXIT
    echo "$DIA"
    $DIA --backtitle "$msg_nchc_free_software_labs" --title \
    "$msg_nchc_clonezilla" --menu "Drive type selection: SD Card for SD card devices or Cvan motion" 0 0 0 \
    "Regular Hard Drive" "Backing up or restoring a regular SSD or HDD" \
    "SD Card" "Making or restoring an image of an SD card with UHS-II optimization" \
    2> "$TMP"
    local dmode="$(cat "$TMP")"
    [ -f "$TMP" ] && rm -f "$TMP"

    case "$dmode" in
        "Regular Hard Drive")
            drivetype="HDD"
            enable_uhs2_optimization="false"
            ;;
        "SD Card")
            drivetype="SD"
            enable_uhs2_optimization="true"
            ;;
        *)
            [ "$BOOTUP" = "color" ] && $SETCOLOR_FAILURE
            echo "Program terminated!"
            [ "$BOOTUP" = "color" ] && $SETCOLOR_NORMAL
            exit 1
            ;;
    esac
}

backup_or_restore() {
    local TMP="$(mktemp /tmp/menu.XXXXXX)"
    trap "[ -f \"$TMP\" ] && rm -f \"$TMP\"" HUP INT QUIT TERM EXIT
    echo "$DIA"
    $DIA --backtitle "$msg_nchc_free_software_labs" --title \
    "$msg_nchc_clonezilla" --menu "Choose operation:" 0 0 0 \
    "Backup" "Save an image of a disk" \
    "Restore" "Restore an image to a disk" \
    2> "$TMP"
    local dmode="$(cat "$TMP")"
    [ -f "$TMP" ] && rm -f "$TMP"

    case "$dmode" in
        "Backup")
            optype="savedisk"
            ;;
        "Restore")
            optype="restoredisk"
            ;;
        *)
            [ "$BOOTUP" = "color" ] && $SETCOLOR_FAILURE
            echo "Program terminated!"
            [ "$BOOTUP" = "color" ] && $SETCOLOR_NORMAL
            exit 1
            ;;
    esac
}

# ============================================================================
# DEVICE DETECTION FUNCTIONS
# ============================================================================

# Preset list of WWIDs to exclude from device selection
preset_list=("naa.53a5a27678000f64" "naa.5000c5004ec221d9" "naa.5000c500a2e75db0" "naa.5000c5004ec9aec8" "naa.5000c5007a43f99e" "naa.5000c500a5523e8d" "naa.5000c500408fd9d9" "naa.50014ee20f256a95")

#
# is_device_excluded() - Check if device WWID is in exclusion list
#
# Parameters:
#   $1 - WWID to check
#
# Returns:
#   0 if excluded, 1 if not excluded
#
is_device_excluded() {
    local wwid="$1"

    for preset in "${preset_list[@]}"; do
        if [[ "$wwid" == "$preset" ]]; then
            return 0  # Device is excluded
        fi
    done
    return 1  # Device is not excluded
}

#
# get_device_wwid() - Get WWID for a device
#
# Parameters:
#   $1 - Device path in /sys/block format
#
# Returns:
#   Echoes WWID or empty string if not available
#
get_device_wwid() {
    local device="$1"
    local dev_name="/dev/$(basename "$device")"

    if [[ -e "$device/device/wwid" ]] && [[ -r "$device/device/wwid" ]]; then
        local wwid=$(cat "$device/device/wwid" 2>/dev/null)

        if [[ -z "$wwid" ]]; then
            # Use udevadm to check if the device is a real disk
            if udevadm info --query=all --name="$dev_name" | grep -q "ID_TYPE=disk"; then
                echo "unknown_$(basename "$device")"
            fi
        else
            echo "$wwid"
        fi
    fi
}

#
# get_real_devs() - Find real devices excluding preset WWIDs
#
# Description:
#   Populates the realdevs array with devices that are not in the exclusion list
#
get_real_devs() {
    # Initialize arrays
    realdevs=()
    menu_options=()
    declare -A valid_devices

    # Process each SCSI device
    for device in /sys/block/sd*; do
        local dev_name="/dev/$(basename "$device")"
        local wwid=$(get_device_wwid "$device")

        if [[ -n "$wwid" ]]; then
            echo "$(basename "$device"): $wwid"

            if ! is_device_excluded "$wwid"; then
                valid_devices[$dev_name]=1
            fi
        fi
    done

    # Populate realdevs array
    for dev in "${!valid_devices[@]}"; do
        realdevs+=("$dev")
    done
}


# ============================================================================
# MAIN EXECUTION
# ============================================================================

# Scan for archive disks
scan_and_select_archive_disk

# Present archive disk selection menu
TMP="$(mktemp /tmp/menu.XXXXXX)"
trap "[ -f \"$TMP\" ] && rm -f \"$TMP\"" HUP INT QUIT TERM EXIT

$DIA --backtitle "$msg_nchc_free_software_labs" --title \
    "$msg_nchc_clonezilla" --menu "Choose Archive Disk:" 0 0 0 \
    "1018 Archive Disk" "${device_paths[efis]}" \
    "1329 Archive Disk" "${device_paths[pl21]}" \
    "C208 Archive Disk" "${device_paths[c208]}" \
    "Dash Archive Disk" "${device_paths[dash]}" \
    "1678/1697 Archive Disk" "${device_paths[ka_g1000]}" \
    "C90 Archive Disk" "${device_paths[c90]}" \
    "Internal Archive Disk" "${device_paths[internal]}" \
    "None" "Opt not to select and cancel" \
    2> "$TMP"

mode="$(cat "$TMP")"
[ -f "$TMP" ] && rm -f "$TMP"

# Process archive disk selection
case "$mode" in
    "1018 Archive Disk")
        selected_dev="${device_paths[efis]}"
        ;;
    "1329 Archive Disk")
        selected_dev="${device_paths[pl21]}"
        ;;
    "C208 Archive Disk")
        selected_dev="${device_paths[c208]}"
        ;;
    "Dash Archive Disk")
        selected_dev="${device_paths[dash]}"
        ;;
    "1678/1697 Archive Disk")
        selected_dev="${device_paths[ka_g1000]}"
        ;;
    "C90 Archive Disk")
        selected_dev="${device_paths[c90]}"
        ;;
    "Internal Archive Disk")
        selected_dev="${device_paths[internal]}"
        ;;
    "None")
        selected_dev="not found"
        ;;
    *)
        error_exit "Invalid archive disk selection"
        ;;
esac

# Mount the selected archive disk
selected_str="$mode"
current_mount=$(findmnt -U /home/<USER>'/dev/{print $2}')

if [ -n "$current_mount" ]; then
    echo "/home/<USER>"
    umount "$current_mount" 2>/dev/null
fi

if [ "$selected_dev" != "not found" ] && [ -n "$selected_dev" ] && [ -b "$selected_dev" ]; then
    echo "Mounting \"$mode\" at \"$selected_dev\" to /home/<USER>"

    # Unmount the device first if it's already mounted
    umount "$selected_dev" 2>/dev/null

    # Attempt to mount the device
    if mount "$selected_dev" /home/<USER>/dev/null; then
        echo "Successfully mounted $selected_dev to /home/<USER>"
    else
        error_exit "Failed to mount $selected_dev to /home/<USER>"
    fi
else
    error_exit "Selected disk not present, not found, or elected not to mount a drive. Selected device: '$selected_dev'"
fi

# Get user preferences for drive type and operation
sd_card_or_hd
backup_or_restore

# Initialize device selection menu
menu_options=()

if [[ "$drivetype" == "HDD" ]]; then
    # For HDD drives, use the existing get_real_devs logic
    get_real_devs

    # Process each device in realdevs array properly, filtering out drives without serial numbers
    for dev in "${realdevs[@]}"; do
        echo "Processing device: $dev"
        found_dev_info=$(find_dev "$dev" 2>/dev/null)
        if [ $? -eq 0 ] && [ -n "$found_dev_info" ]; then
            menu_options+=("$dev" "$found_dev_info")
            echo "Added to menu: $dev - $found_dev_info"
        else
            echo "Skipping device $dev - no serial number found"
        fi
    done

elif [[ "$drivetype" == "SD" ]]; then
    # For SD cards, find the UHS-II slot and parse SCSI2SD config
    echo "SD card selected - detecting UHS-II slot and parsing SCSI2SD config..."

    # Find the UHS-II slot device
    uhs2_device=$(detect_uhs2_slot)
    if [ $? -eq 0 ] && [ -n "$uhs2_device" ]; then
        echo "Found UHS-II slot: $uhs2_device"

        # Parse SCSI2SD config to get device information
        echo "Parsing SCSI2SD configuration..."
        target_serial_number=$(get_scsi2sd_serial_number "$uhs2_device")

        if [ -n "$target_serial_number" ]; then
            echo "Using SCSI2SD config serial number: '$target_serial_number'"

            # Query database with the serial number from SCSI2SD config
            found_dev_info=$(find_dev "$target_serial_number" "by_serial")
            menu_options+=("$uhs2_device" "$found_dev_info")
            echo "Added SD card to menu: $uhs2_device - $found_dev_info"
        else
            echo "Warning: Could not parse SCSI2SD config, using device serial number"
            found_dev_info=$(find_dev "$uhs2_device")
            menu_options+=("$uhs2_device" "$found_dev_info")
            echo "Added SD card to menu: $uhs2_device - $found_dev_info"
        fi
    else
        error_exit "UHS-II slot not found"
    fi
else
    error_exit "Unknown drive type: $drivetype"
fi

# Check if any valid drives were found
if [ ${#menu_options[@]} -eq 0 ]; then
    error_exit "No drives with valid serial numbers found"
fi

target_dev=$(whiptail --title "Select Target Device" --menu "Choose a Target:" 35 125 8 "${menu_options[@]}" 3>&1 1>&2 2>&3)
echo "Target dev selected is \"$target_dev\""

# Determine serial number based on drive type
sd_card_has_config=true
if [ "$drivetype" == "SD" ]; then
    # For SD cards, try to get serial number from SCSI2SD config
    uhs2_device=$(detect_uhs2_slot)

    if [ -z "$uhs2_device" ]; then
        echo "Warning: Could not detect UHS-II slot device"
        error_exit "Cannot proceed with SD card operation - UHS-II slot not detected"
    else
        target_serial_number=$(get_scsi2sd_serial_number "$uhs2_device")

        if [ -z "$target_serial_number" ]; then
            echo "No SCSI2SD config found on SD card - this may be a new/blank card"
            sd_card_has_config=false

            # For both backup and restore operations, offer to write a config or cancel
            show_blank_sd_card_menu "$uhs2_device"
        fi
    fi
else
    # For HDD, use traditional method
    target_serial_number=$(sudo hdparm -I "$target_dev" | grep "Serial Number" | awk '{print $3}')

    if [ -z "$target_serial_number" ]; then
        error_exit "Could not get serial number for HDD device $target_dev"
    fi
fi

    # Check if the serial number exists in the database
    echo "DEBUG: target_serial_number = $target_serial_number"

    db_query_result=$(sqlite3 "$database_path" <<EOF
.header off
.mode list
SELECT "Serial Number" FROM $table WHERE "Serial Number" = '$target_serial_number';
EOF
    )

    in_db="false"
    if [ -n "$db_query_result" ]; then
        in_db="true"
    else
        echo "Serial number $target_serial_number not found in the database."
    fi
    echo "Database check result: $in_db"
    if [ "$in_db" = "false" ]; then
        whiptail --backtitle "$msg_nchc_free_software_labs" --title "Drive not found in database" \
            --yesno "$target_dev $target_serial_number: The serial number of this drive could not be found in the database.\nIf this is an existing drive with an asset tag, the serial number might have been saved incorrectly.\nIf this is a new drive or is meant to replace a drive, you can add it to the database.\n\nDo you want to add or modify the database to include this drive?" 12 120

        user_choice=$?
        case "$user_choice" in
            0)
                add_or_modify_drive_to_DB "$target_dev"
                # After adding to database, we need to re-query to get the drive details
                echo "Re-querying database after adding drive..."

                # Debug: Check if the serial number now exists in database
                local check_result=$(sqlite3 "$database_path" "SELECT COUNT(*) FROM $table WHERE \"Serial Number\" = '$target_serial_number';")
                echo "DEBUG: Database check after update - found $check_result entries for serial $target_serial_number"
                ;;
            1)
                echo "Skipping database update for this drive."
                ;;
            *)
                error_exit "Database update operation cancelled"
                ;;
        esac
    else
        echo "Continuing with existing database entry"
    fi

# Generate timestamps for backup naming and logging
current_date=$(date '+%m-%d-%y:%H:%M')
current_date_database=$(date '+%m_%d_%Y')

# Query the database for drive details
echo "DEBUG: Querying database for serial number: '$target_serial_number'"
drive_details=$(sqlite3 "$database_path" <<EOF
.header off
.mode list
SELECT "Computer", COALESCE("Revision Number", 0), "Sibling Drives", "Simulator", "Letter/PriSec", "Asset Tag" FROM $table WHERE "Serial Number" = '$target_serial_number';
EOF
)

echo "Drive details: $drive_details"
echo "DEBUG: Drive details length: ${#drive_details}"
computer=$(echo "$drive_details" | awk -F '|' '{print $1}')
simulator=$(echo "$drive_details" | awk -F '|' '{print $4}')
letter=$(echo "$drive_details" | awk -F '|' '{print $5}')
rev_number=$(echo "$drive_details" | awk -F '|' '{print $2}')
sibling_drives=$(echo "$drive_details" | awk -F '|' '{print $3}')
asset_tag=$(echo "$drive_details" | awk -F '|' '{print $6}')

# Debug output for revision number
echo "DEBUG: Raw revision number from database: '$rev_number'"

# Handle empty or invalid revision numbers
if [ -z "$rev_number" ] || ! [[ "$rev_number" =~ ^[0-9]+$ ]]; then
    echo "WARNING: Invalid or empty revision number '$rev_number', defaulting to 0"
    rev_number=0
fi

echo "DEBUG: Using revision number: $rev_number"

# Clean up computer name for file naming (replace spaces with underscores)
computer=${computer// /_}

# Unmount any existing partitions on the target device
for partition in "$target_dev"*; do
    if [ -b "$partition" ]; then
        umount "$partition" 2>/dev/null || true
    fi
done

# Execute backup or restore operation
if [ "$optype" = "savedisk" ]; then
    new_rev_number=$((rev_number + 1))
    echo "DEBUG: Current revision: $rev_number, New revision: $new_rev_number"
    backup_name="${simulator}-${computer}-${current_date_database}-${new_rev_number}-img"
    echo "DEBUG: Backup name: $backup_name"

    if [[ "$drivetype" == "HDD" ]]; then
        backup_standard_drive_action "$optype" "$target_dev" "$backup_name" "$new_rev_number" "$asset_tag" "$target_serial_number" "$simulator" "$computer"
        log_drive_action "$optype" "$target_dev" "$backup_name" "$new_rev_number" "$asset_tag" "$target_serial_number" "$simulator" "$computer" "$letter" "$current_date"
    elif [[ "$drivetype" == "SD" ]]; then
        SD_drive_action "$optype" "$target_dev" "$backup_name" "$new_rev_number" "$asset_tag" "$target_serial_number" "$simulator" "$computer" "$enable_uhs2_optimization"
        log_drive_action "$optype" "$target_dev" "$backup_name" "$new_rev_number" "$asset_tag" "$target_serial_number" "$simulator" "$computer" "$letter" "$current_date"
    else
        error_exit "Unknown drive type for backup: $drivetype"
    fi
else
    # Restore operation
    restore_info_file=$(mktemp)
    echo "DEBUG: Calling get_restore_img with simulator=$simulator, computer=$computer, drivetype=$drivetype"
    get_restore_img "$simulator" "$computer" "$drivetype" > "$restore_info_file"

    if [[ ! -s "$restore_info_file" ]]; then
        rm -f "$restore_info_file"
        error_exit "No restore image information returned"
    fi

    read -r selected_image selected_revision < "$restore_info_file"
    rm -f "$restore_info_file"

    echo "Selected restore image: \"$selected_image\""
    create_restore_command "$selected_image" "$target_dev" "$selected_revision" "$asset_tag" "$target_serial_number" "$drivetype"



    log_drive_action "$optype" "$target_dev" "$selected_image" "$selected_revision" "$asset_tag" "$target_serial_number" "$simulator" "$computer" "$letter" "$current_date"
fi
