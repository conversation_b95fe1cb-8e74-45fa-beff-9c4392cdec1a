#!/bin/bash

# Test script to verify simulator mapping from archive disk selection

# Source the functions
. /home/<USER>/Desktop/clonezilla-master/usr/sbin/ocs-flyright-functions

# Test the mapping function
test_selections=(
    "1018 Archive Disk"
    "1329 Archive Disk"
    "C208 Archive Disk"
    "Dash Archive Disk"
    "1678/1697 Archive Disk"
    "C90 Archive Disk"
    "Internal Archive Disk"
)

echo "Testing simulator mapping from archive disk selections:"
echo "======================================================"

for selection in "${test_selections[@]}"; do
    simulator=$(get_simulator_from_archive_selection "$selection")
    echo "Archive Selection: '$selection' -> Simulator: '$simulator'"
done

echo ""
echo "Testing with database query to verify simulators exist:"
echo "======================================================"

database_path="/home/<USER>/Desktop/DriveDatabase/FlyrightDriveDatabase.db"

for selection in "${test_selections[@]}"; do
    simulator=$(get_simulator_from_archive_selection "$selection")
    if [ -n "$simulator" ]; then
        # Check if this simulator exists in database
        exists=$(sqlite3 "$database_path" "SELECT COUNT(*) FROM Drives WHERE Simulator = '$simulator';" 2>/dev/null)
        if [ "$exists" -gt 0 ]; then
            echo "✓ '$selection' -> '$simulator' (Found $exists entries in database)"
        else
            echo "✗ '$selection' -> '$simulator' (NOT found in database)"
        fi
    else
        echo "✗ '$selection' -> (No mapping found)"
    fi
done
